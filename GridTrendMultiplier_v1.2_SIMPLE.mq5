//+------------------------------------------------------------------+
//| GridTrendMultiplier_v1.2_SIMPLE.mq5                             |
//| SIMPLIFIED VERSION - No Recycling, Just Close at Grid Profit   |
//| Version: 1.2 - Back to basics, no profit recycling             |
//+------------------------------------------------------------------+
#property copyright "GridTrendMultiplier"
#property version   "1.20"

//--- Enums
enum ENUM_DIRECTION
{
    DIR_BUY = 0,     // BUY grids only
    DIR_SELL = 1     // SELL grids only
};

//--- Input parameters
input ENUM_DIRECTION Direction = DIR_BUY;    // Grid Direction
input double LotSize = 0.10;                 // Fixed lot for every leg
input int GridSizePips = 20;                 // Distance between entries and per-leg TP
input int MaxGrids = 10;                     // Maximum simultaneous positions
input double ProfitTargetPct = 5.0;          // % equity gain that triggers shutdown
input int SessionStartHour = 0;              // First hour when trading is allowed
input int SessionEndHour = 23;               // Last hour when trading is allowed
input uint Magic = 555555;                   // Unique magic number

//--- Global variables
double GridSizePoints = 0.0;                 // Grid size in points
double StartingBalance = 0.0;                // Starting balance for the day
double BestOpenPrice = 0.0;                  // Best open price (reference for pullbacks)
int CurrentGridCount = 0;                    // Current number of open positions
bool PausedForDay = false;                   // Daily profit target reached flag
datetime LastResetDate = 0;                  // Last daily reset date

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate grid size in points
    GridSizePoints = GridSizePips * _Point;
    if (_Digits == 5 || _Digits == 3)
        GridSizePoints = GridSizePips * _Point * 10;
    
    // Initialize starting balance
    StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();
    
    Print("=== GridTrendMultiplier EA v1.2 SIMPLE ===");
    Print("*** TRADING DIRECTION: ", EnumToString(Direction), " ONLY ***");
    Print("*** NO PROFIT RECYCLING - JUST CLOSE AT GRID PROFIT ***");
    Print("Lot Size: ", LotSize);
    Print("Grid Size: ", GridSizePips, " pips");
    Print("Max Grids: ", MaxGrids);
    Print("Profit Target: ", ProfitTargetPct, "%");
    Print("Magic: ", Magic);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("GridTrendMultiplier EA v1.2 SIMPLE deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Daily reset check
    if (IsNewDay())
    {
        DailyReset();
    }
    
    // Session gate - ignore ticks outside trading hours
    if (!IsWithinSession())
    {
        return;
    }
    
    // Skip if paused for the day
    if (PausedForDay)
    {
        return;
    }
    
    // Update grid information
    UpdateGridInfo();
    
    // Daily profit-stop check
    if (CheckDailyProfitTarget())
    {
        CloseAllPositions();
        PausedForDay = true;
        Print("Daily profit target reached! EA paused until tomorrow.");
        return;
    }
    
    // Close positions that reached grid size profit (NO RECYCLING)
    CloseGridProfitPositions();
    
    // Seed trade - open first position if none exist
    if (CurrentGridCount == 0)
    {
        OpenSeedTrade();
        return;
    }
    
    // Pull-back adds - add new legs on retracements
    CheckPullbackAdds();
}

//+------------------------------------------------------------------+
//| Check if new day for daily reset                                |
//+------------------------------------------------------------------+
bool IsNewDay()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime currentDT, lastDT;
    
    TimeToStruct(currentTime, currentDT);
    TimeToStruct(LastResetDate, lastDT);
    
    return (currentDT.day != lastDT.day || currentDT.mon != lastDT.mon || currentDT.year != lastDT.year);
}

//+------------------------------------------------------------------+
//| Daily reset function                                            |
//+------------------------------------------------------------------+
void DailyReset()
{
    PausedForDay = false;
    StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();
    
    Print("=== DAILY RESET ===");
    Print("New starting balance: $", DoubleToString(StartingBalance, 2));
    Print("EA ready for new trading day");
}

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    if (SessionStartHour <= SessionEndHour)
    {
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    }
    else
    {
        // Overnight session (e.g., 22:00 - 06:00)
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Update grid information                                          |
//+------------------------------------------------------------------+
void UpdateGridInfo()
{
    CurrentGridCount = 0;
    BestOpenPrice = 0.0;
    bool firstPosition = true;
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                // Safety check: Only count positions in our chosen direction
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isCorrectDirection = (Direction == DIR_BUY && posType == POSITION_TYPE_BUY) ||
                                        (Direction == DIR_SELL && posType == POSITION_TYPE_SELL);
                
                if (!isCorrectDirection)
                {
                    Print("WARNING: Found position in wrong direction! Expected: ", EnumToString(Direction), 
                          " Found: ", EnumToString(posType));
                    continue; // Skip this position
                }
                
                CurrentGridCount++;
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                
                if (firstPosition)
                {
                    BestOpenPrice = openPrice;
                    firstPosition = false;
                }
                else
                {
                    // Track best open price (lowest for BUY, highest for SELL)
                    if (Direction == DIR_BUY)
                    {
                        if (openPrice < BestOpenPrice)
                            BestOpenPrice = openPrice;
                    }
                    else
                    {
                        if (openPrice > BestOpenPrice)
                            BestOpenPrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check daily profit target                                       |
//+------------------------------------------------------------------+
bool CheckDailyProfitTarget()
{
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double targetEquity = StartingBalance * (1.0 + ProfitTargetPct / 100.0);
    
    if (currentEquity >= targetEquity)
    {
        double profit = currentEquity - StartingBalance;
        Print("Daily profit target reached! Profit: $", DoubleToString(profit, 2), 
              " (", DoubleToString(ProfitTargetPct, 1), "%)");
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Close positions that reached grid size profit (NO RECYCLING)   |
//+------------------------------------------------------------------+
void CloseGridProfitPositions()
{
    double currentPrice = (Direction == DIR_BUY) ? 
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) : 
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                // Safety check: Only process positions in our chosen direction
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isCorrectDirection = (Direction == DIR_BUY && posType == POSITION_TYPE_BUY) ||
                                        (Direction == DIR_SELL && posType == POSITION_TYPE_SELL);
                
                if (!isCorrectDirection)
                    continue;
                
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double positionProfit = PositionGetDouble(POSITION_PROFIT);
                bool shouldClose = false;
                
                // Check if position has reached grid size profit
                if (positionProfit > 0)
                {
                    if (Direction == DIR_BUY)
                    {
                        // For BUY: check if price moved GridSizePips above open price
                        shouldClose = (currentPrice - openPrice) >= GridSizePoints;
                    }
                    else
                    {
                        // For SELL: check if price moved GridSizePips below open price
                        shouldClose = (openPrice - currentPrice) >= GridSizePoints;
                    }
                }
                
                if (shouldClose)
                {
                    Print("Closing position at grid profit: $", DoubleToString(positionProfit, 2), " (NO RECYCLING)");
                    ClosePosition(PositionGetTicket(i));
                    // NO RECYCLING - just close and that's it
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open seed trade                                                 |
//+------------------------------------------------------------------+
void OpenSeedTrade()
{
    Print("=== OPENING SEED TRADE ===");
    OpenGridPosition();
}

//+------------------------------------------------------------------+
//| Check pullback adds                                             |
//+------------------------------------------------------------------+
void CheckPullbackAdds()
{
    if (CurrentGridCount >= MaxGrids)
        return;

    double currentPrice = (Direction == DIR_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    bool shouldAdd = false;

    if (Direction == DIR_BUY)
    {
        // Add BUY if price retraced GridSizePips from best (lowest) price
        shouldAdd = (BestOpenPrice - currentPrice) >= GridSizePoints;
    }
    else
    {
        // Add SELL if price retraced GridSizePips from best (highest) price
        shouldAdd = (currentPrice - BestOpenPrice) >= GridSizePoints;
    }

    if (shouldAdd)
    {
        Print("=== PULLBACK ADD: Adding new position ===");
        OpenGridPosition();
    }
}

//+------------------------------------------------------------------+
//| Open grid position (core function)                              |
//+------------------------------------------------------------------+
void OpenGridPosition()
{
    Print("*** Opening ", EnumToString(Direction), " position ***");

    ENUM_ORDER_TYPE orderType = (Direction == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    double price = (Direction == DIR_BUY) ?
                   SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                   SymbolInfoDouble(Symbol(), SYMBOL_BID);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = orderType;
    request.price = price;
    request.tp = 0.0;  // No TP - manual close
    request.sl = 0.0;  // No SL
    request.magic = Magic;
    request.comment = "GTM_v1.2";

    bool success = OrderSend(request, result);

    if (success)
    {
        Print("SUCCESS: ", EnumToString(orderType), " ", LotSize, " lots at ", DoubleToString(price, _Digits));
    }
    else
    {
        Print("FAILED: Error ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Close single position                                           |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket)
{
    if (!PositionSelectByTicket(ticket))
        return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                   SymbolInfoDouble(request.symbol, SYMBOL_BID) :
                   SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.magic = Magic;
    request.comment = "GTM_Close";

    if (OrderSend(request, result))
    {
        Print("Position closed: Ticket ", ticket);
    }
    else
    {
        Print("Failed to close position ", ticket, " Error: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ulong ticket = PositionGetTicket(i);
                ClosePosition(ticket);
                closedCount++;
            }
        }
    }

    Print("=== DAILY TARGET REACHED ===");
    Print("Closed ", closedCount, " positions");

    // Reset counters
    CurrentGridCount = 0;
    BestOpenPrice = 0.0;
}
