//+------------------------------------------------------------------+
//| GridTrendMultiplier_v1.3_GRIDLEVELS.mq5                         |
//| GRID LEVELS VERSION - One Trade Per Grid Level Only             |
//| Version: 1.3 - Enforces strict grid levels                     |
//+------------------------------------------------------------------+
#property copyright "GridTrendMultiplier"
#property version   "1.30"

//--- Enums
enum ENUM_DIRECTION
{
    DIR_BUY = 0,     // BUY grids only
    DIR_SELL = 1     // SELL grids only
};

//--- Input parameters
input ENUM_DIRECTION Direction = DIR_BUY;    // Grid Direction
input double LotSize = 0.10;                 // Fixed lot for every leg
input int GridSizePips = 20;                 // Distance between entries and per-leg TP
input int MaxGrids = 10;                     // Maximum simultaneous positions
input double ProfitTargetPct = 5.0;          // % equity gain that triggers shutdown
input int SessionStartHour = 0;              // First hour when trading is allowed
input int SessionEndHour = 23;               // Last hour when trading is allowed
input uint Magic = 555555;                   // Unique magic number

//--- Global variables
double GridSizePoints = 0.0;                 // Grid size in points
double StartingBalance = 0.0;                // Starting balance for the day
double GridLevels[100];                      // Array to store occupied grid levels
int GridLevelCount = 0;                      // Number of occupied grid levels
int CurrentGridCount = 0;                    // Current number of open positions
bool PausedForDay = false;                   // Daily profit target reached flag
datetime LastResetDate = 0;                  // Last daily reset date
datetime LastActionTime = 0;                 // Prevent multiple actions per second

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate grid size in points
    GridSizePoints = GridSizePips * _Point;
    if (_Digits == 5 || _Digits == 3)
        GridSizePoints = GridSizePips * _Point * 10;
    
    // Initialize starting balance
    StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();
    
    // Clear grid levels
    ArrayInitialize(GridLevels, 0.0);
    GridLevelCount = 0;
    
    Print("=== GridTrendMultiplier EA v1.3 GRID LEVELS ===");
    Print("*** TRADING DIRECTION: ", EnumToString(Direction), " ONLY ***");
    Print("*** ONE TRADE PER GRID LEVEL ONLY ***");
    Print("Lot Size: ", LotSize);
    Print("Grid Size: ", GridSizePips, " pips (", DoubleToString(GridSizePoints, _Digits), " points)");
    Print("Max Grids: ", MaxGrids);
    Print("Magic: ", Magic);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("GridTrendMultiplier EA v1.3 GRID LEVELS deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Prevent multiple actions per second
    datetime currentTime = TimeCurrent();
    if (currentTime == LastActionTime)
        return;
    
    // Daily reset check
    if (IsNewDay())
    {
        DailyReset();
    }
    
    // Session gate
    if (!IsWithinSession())
        return;
    
    // Skip if paused
    if (PausedForDay)
        return;
    
    // Update grid information
    UpdateGridInfo();
    
    // Daily profit check
    if (CheckDailyProfitTarget())
    {
        CloseAllPositions();
        PausedForDay = true;
        Print("Daily profit target reached! EA paused until tomorrow.");
        return;
    }
    
    // Close positions at grid profit
    if (CloseGridProfitPositions())
    {
        LastActionTime = currentTime;
        return; // Exit after closing to prevent multiple actions
    }
    
    // Open first position if none exist
    if (CurrentGridCount == 0)
    {
        OpenFirstPosition();
        LastActionTime = currentTime;
        return;
    }
    
    // Check for new grid level
    if (ShouldOpenNewGridLevel())
    {
        OpenNewGridLevel();
        LastActionTime = currentTime;
        return;
    }
}

//+------------------------------------------------------------------+
//| Check if new day                                                |
//+------------------------------------------------------------------+
bool IsNewDay()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime currentDT, lastDT;
    
    TimeToStruct(currentTime, currentDT);
    TimeToStruct(LastResetDate, lastDT);
    
    return (currentDT.day != lastDT.day || currentDT.mon != lastDT.mon || currentDT.year != lastDT.year);
}

//+------------------------------------------------------------------+
//| Daily reset                                                     |
//+------------------------------------------------------------------+
void DailyReset()
{
    PausedForDay = false;
    StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();
    
    Print("=== DAILY RESET ===");
    Print("New starting balance: $", DoubleToString(StartingBalance, 2));
}

//+------------------------------------------------------------------+
//| Check if within session                                         |
//+------------------------------------------------------------------+
bool IsWithinSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    if (SessionStartHour <= SessionEndHour)
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    else
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
}

//+------------------------------------------------------------------+
//| Update grid information                                          |
//+------------------------------------------------------------------+
void UpdateGridInfo()
{
    CurrentGridCount = 0;
    GridLevelCount = 0;
    ArrayInitialize(GridLevels, 0.0);
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                // Check direction
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isCorrectDirection = (Direction == DIR_BUY && posType == POSITION_TYPE_BUY) ||
                                        (Direction == DIR_SELL && posType == POSITION_TYPE_SELL);
                
                if (!isCorrectDirection)
                {
                    Print("WARNING: Wrong direction position found!");
                    continue;
                }
                
                CurrentGridCount++;
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                
                // Store this grid level
                if (GridLevelCount < 100)
                {
                    GridLevels[GridLevelCount] = openPrice;
                    GridLevelCount++;
                }
            }
        }
    }
    
    Print("Grid Info: ", CurrentGridCount, " positions at ", GridLevelCount, " levels");
}

//+------------------------------------------------------------------+
//| Check daily profit target                                       |
//+------------------------------------------------------------------+
bool CheckDailyProfitTarget()
{
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double targetEquity = StartingBalance * (1.0 + ProfitTargetPct / 100.0);
    
    return (currentEquity >= targetEquity);
}

//+------------------------------------------------------------------+
//| Close positions at grid profit                                  |
//+------------------------------------------------------------------+
bool CloseGridProfitPositions()
{
    double currentPrice = (Direction == DIR_BUY) ? 
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) : 
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double positionProfit = PositionGetDouble(POSITION_PROFIT);
                bool shouldClose = false;
                
                if (positionProfit > 0)
                {
                    if (Direction == DIR_BUY)
                        shouldClose = (currentPrice - openPrice) >= GridSizePoints;
                    else
                        shouldClose = (openPrice - currentPrice) >= GridSizePoints;
                }
                
                if (shouldClose)
                {
                    Print("Closing position at grid profit: $", DoubleToString(positionProfit, 2));
                    ClosePosition(PositionGetTicket(i));
                    return true; // Return after closing ONE position
                }
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Open first position                                             |
//+------------------------------------------------------------------+
void OpenFirstPosition()
{
    Print("=== OPENING FIRST POSITION ===");
    
    double currentPrice = (Direction == DIR_BUY) ? 
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) : 
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    OpenPositionAtPrice(currentPrice);
}

//+------------------------------------------------------------------+
//| Check if should open new grid level                             |
//+------------------------------------------------------------------+
bool ShouldOpenNewGridLevel()
{
    if (CurrentGridCount >= MaxGrids)
        return false;
    
    double currentPrice = (Direction == DIR_BUY) ? 
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) : 
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // Calculate the next grid level that should be opened
    double nextGridLevel = CalculateNextGridLevel(currentPrice);
    
    if (nextGridLevel == 0.0)
        return false; // No valid grid level
    
    // Check if we already have a position at this level
    if (IsGridLevelOccupied(nextGridLevel))
        return false; // Already have position at this level
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate next grid level to open                               |
//+------------------------------------------------------------------+
double CalculateNextGridLevel(double currentPrice)
{
    if (GridLevelCount == 0)
        return 0.0; // No existing positions
    
    // Find the closest existing grid level
    double closestLevel = GridLevels[0];
    double minDistance = MathAbs(currentPrice - closestLevel);
    
    for (int i = 1; i < GridLevelCount; i++)
    {
        double distance = MathAbs(currentPrice - GridLevels[i]);
        if (distance < minDistance)
        {
            minDistance = distance;
            closestLevel = GridLevels[i];
        }
    }
    
    // Check if current price is far enough for a new grid
    if (Direction == DIR_BUY)
    {
        // For BUY: open new grid if price dropped GridSizePoints below closest level
        if ((closestLevel - currentPrice) >= GridSizePoints)
        {
            // Calculate exact grid level
            double gridLevel = closestLevel - GridSizePoints;
            return gridLevel;
        }
    }
    else
    {
        // For SELL: open new grid if price rose GridSizePoints above closest level
        if ((currentPrice - closestLevel) >= GridSizePoints)
        {
            // Calculate exact grid level
            double gridLevel = closestLevel + GridSizePoints;
            return gridLevel;
        }
    }
    
    return 0.0; // No new grid level needed
}

//+------------------------------------------------------------------+
//| Check if grid level is already occupied                         |
//+------------------------------------------------------------------+
bool IsGridLevelOccupied(double level)
{
    double tolerance = GridSizePoints * 0.1; // 10% tolerance

    for (int i = 0; i < GridLevelCount; i++)
    {
        if (MathAbs(GridLevels[i] - level) <= tolerance)
            return true; // Level is occupied
    }

    return false; // Level is free
}

//+------------------------------------------------------------------+
//| Open new grid level                                             |
//+------------------------------------------------------------------+
void OpenNewGridLevel()
{
    double currentPrice = (Direction == DIR_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    double nextGridLevel = CalculateNextGridLevel(currentPrice);

    if (nextGridLevel != 0.0 && !IsGridLevelOccupied(nextGridLevel))
    {
        Print("=== OPENING NEW GRID LEVEL ===");
        Print("Target level: ", DoubleToString(nextGridLevel, _Digits));
        Print("Current price: ", DoubleToString(currentPrice, _Digits));

        OpenPositionAtPrice(nextGridLevel);
    }
}

//+------------------------------------------------------------------+
//| Open position at specific price                                 |
//+------------------------------------------------------------------+
void OpenPositionAtPrice(double targetPrice)
{
    ENUM_ORDER_TYPE orderType = (Direction == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

    // Use current market price (we can't control exact entry price in market orders)
    double price = (Direction == DIR_BUY) ?
                   SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                   SymbolInfoDouble(Symbol(), SYMBOL_BID);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = orderType;
    request.price = price;
    request.tp = 0.0;
    request.sl = 0.0;
    request.magic = Magic;
    request.comment = "GTM_v1.3";

    bool success = OrderSend(request, result);

    if (success)
    {
        Print("SUCCESS: ", EnumToString(orderType), " ", LotSize, " lots at ", DoubleToString(price, _Digits));
        Print("Grid level: ", DoubleToString(targetPrice, _Digits));
    }
    else
    {
        Print("FAILED: Error ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Close single position                                           |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket)
{
    if (!PositionSelectByTicket(ticket))
        return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                   SymbolInfoDouble(request.symbol, SYMBOL_BID) :
                   SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.magic = Magic;
    request.comment = "GTM_Close";

    if (OrderSend(request, result))
    {
        Print("Position closed: Ticket ", ticket);
    }
    else
    {
        Print("Failed to close position ", ticket);
    }
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ClosePosition(PositionGetTicket(i));
                closedCount++;
            }
        }
    }

    Print("=== DAILY TARGET REACHED ===");
    Print("Closed ", closedCount, " positions");

    // Reset
    CurrentGridCount = 0;
    GridLevelCount = 0;
    ArrayInitialize(GridLevels, 0.0);
}
